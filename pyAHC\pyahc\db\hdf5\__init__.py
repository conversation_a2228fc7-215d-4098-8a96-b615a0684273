"""pyAHC HDF5数据管理模块

提供基于HDF5的项目数据存储和管理功能，支持：
- 项目级别的数据组织
- 日循环模拟的状态传递
- 状态变量的提取和注入
- 数据同化的基础架构
- 完整的工作流程集成
"""

from .manager import ProjectHDF5Manager
from .state_extractor import StateExtractor
from .state_injector import StateInjector
from .utils import get_variable_units, validate_date_format
from .exceptions import (
    HDF5Error,
    ProjectNotFoundError,
    DataNotFoundError,
    InvalidDataFormatError
)
from .workflow import (
    initialize_project_simulation,
    daily_simulation_step,
    generate_project_summary,
    run_project_simulation,
    run_optimized_project_simulation,
    run_batch_simulation
)

__version__ = "0.1.0"
__all__ = [
    # 核心组件
    "ProjectHDF5Manager",
    "StateExtractor",
    "StateInjector",

    # 工具函数
    "get_variable_units",
    "validate_date_format",

    # 异常类
    "HDF5Error",
    "ProjectNotFoundError",
    "DataNotFoundError",
    "InvalidDataFormatError",

    # 工作流程函数
    "initialize_project_simulation",
    "daily_simulation_step",
    "generate_project_summary",
    "run_project_simulation",
    "run_optimized_project_simulation",
    "run_batch_simulation"
]
